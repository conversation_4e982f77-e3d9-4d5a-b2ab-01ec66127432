import React, { useState, useEffect, useRef } from 'react';
import { SafeAreaView } from 'react-native-safe-area-context';
import { ScrollView, RefreshControl, View, StyleSheet } from 'react-native';
import {
  AppHeader,
  ScreenContainer,
  HourlyForecast,
  LocationTabs,
  WeatherMapComponent,
  CurrentWeatherCard,
  WeatherDetailsGrid,
  FishingConditionsCard,
  DailyForecastCard,
  WeatherLoadingState,
  WeatherErrorState,
  SectionTitle
} from '@fishivo/ui';
import { theme } from '@fishivo/shared';
import { useUnits } from '@fishivo/hooks';
import { apiService } from '@fishivo/services';
import { getConditionIcon, getLocationIcon } from '@fishivo/utils';

const WeatherScreen = ({ navigation, route }: any) => {
  const [currentLocationIndex, setCurrentLocationIndex] = useState(0);
  const { userPreferences, convertAndFormat } = useUnits();
  const [savedLocations, setSavedLocations] = useState<any[]>([]);
  const [weatherData, setWeatherData] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [formattedTemperature, setFormattedTemperature] = useState('');
  const [formattedWindSpeed, setFormattedWindSpeed] = useState('');
  const [formattedPressure, setFormattedPressure] = useState('');
  const [formattedDailyTemps, setFormattedDailyTemps] = useState<{[key: number]: {high: string, low: string}}>({});
  const [weatherDetails, setWeatherDetails] = useState<any[]>([]);
  const [mapCoordinates, setMapCoordinates] = useState<[number, number]>([28.9784, 41.0082]);
  const [mapSelectedLocation, setMapSelectedLocation] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);
  const debounceRef = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    const loadSavedLocations = async () => {
      try {
        const locations = await apiService.getUserLocations();
        setSavedLocations(locations);
        if (locations.length > 0) setCurrentLocationIndex(0);
      } catch (e) {
        setSavedLocations([{
          id: 'current',
          name: 'Mevcut Konum',
          type: 'current',
          coordinates: { latitude: 41.0082, longitude: 28.9784 },
          address: 'İstanbul, Türkiye',
          isFavorite: false,
        }]);
      } finally {
        setLoading(false);
      }
    };
    loadSavedLocations();
  }, []);

  const currentLocation = savedLocations[currentLocationIndex];

  const loadWeatherData = async (location: any, isRefresh = false) => {
    try {
      setError(null);
      if (isRefresh) setRefreshing(true);
      const weather = await apiService.getCurrentWeather(
        location.coordinates.latitude,
        location.coordinates.longitude
      );
      setWeatherData(weather);
      setMapCoordinates([location.coordinates.longitude, location.coordinates.latitude]);
    } catch (e: any) {
      setError('Hava durumu verisi alınamadı.');
    } finally {
      if (isRefresh) setRefreshing(false);
    }
  };

  useEffect(() => {
    if (currentLocation) {
      loadWeatherData(currentLocation);
    }
  }, [currentLocation]);

  useEffect(() => {
    const formatWeatherData = async () => {
      if (!weatherData) return;
      const temp = await convertAndFormat(weatherData.current.temperature, 'temperature');
      const wind = await convertAndFormat(weatherData.current.windSpeed, 'speed');
      const pressure = await convertAndFormat(weatherData.current.pressure, 'pressure');
      setFormattedTemperature(temp);
      setFormattedWindSpeed(wind);
      setFormattedPressure(pressure);
      const dailyTemps: {[key: number]: {high: string, low: string}} = {};
      for (let i = 0; i < weatherData.daily.length; i++) {
        const day = weatherData.daily[i];
        const high = await convertAndFormat(day.high, 'temperature');
        const low = await convertAndFormat(day.low, 'temperature');
        dailyTemps[i] = { high, low };
      }
      setFormattedDailyTemps(dailyTemps);
      const details = [
        {
          icon: 'thermometer',
          title: 'Hissedilen',
          value: `${Math.round(weatherData.current.apparent_temperature || weatherData.current.temperature)}°`,
          color: '#FF6B35'
        },
        {
          icon: 'droplets',
          title: 'Nem',
          value: `${weatherData.current.humidity}`,
          unit: '%',
          color: '#4A90E2'
        },
        {
          icon: 'activity',
          title: 'Basınç',
          value: `${Math.round(weatherData.current.pressure)}`,
          unit: 'hPa',
          color: '#7B68EE'
        },
        {
          icon: 'wind',
          title: 'Rüzgar',
          value: `${Math.round(weatherData.current.windSpeed)}`,
          unit: userPreferences.speed === 'kmh' ? 'km/h' : 'mph',
          color: '#32CD32'
        },
        {
          icon: 'compass',
          title: 'Yön',
          value: `${Math.round(weatherData.current.windDirection)}°`,
          color: '#FFD700'
        },
        {
          icon: 'zap',
          title: 'UV İndeksi',
          value: `${weatherData.current.uvIndex || 0}`,
          color: '#FF4500'
        }
      ];
      setWeatherDetails(details);
    };
    formatWeatherData();
  }, [weatherData, units]);

  const handleRefresh = () => {
    if (currentLocation) loadWeatherData(currentLocation, true);
  };

  const openLocationManager = () => {
    // Lokasyon yönetimi modalı açılabilir
  };

  const handleMapLocationSelect = (coordinates: [number, number]) => {
    setMapCoordinates(coordinates);
    if (debounceRef.current) clearTimeout(debounceRef.current);
    debounceRef.current = setTimeout(async () => {
      try {
        const [weather, forecast, locationName] = await Promise.all([
          apiService.getCurrentWeather(coordinates[1], coordinates[0]),
          apiService.getWeatherForecast(coordinates[1], coordinates[0], 7),
          apiService.getLocationName(coordinates[1], coordinates[0])
        ]);
        
        const combinedWeatherData = {
          ...weather,
          ...forecast
        };
        
        setWeatherData(combinedWeatherData);
        
        // Konum adını güncelle
        if (locationName && locationName.name) {
          const newLocation = {
            id: 'map-location',
            name: locationName.name,
            type: 'manual' as const,
            coordinates: {
              latitude: coordinates[1],
              longitude: coordinates[0]
            },
            address: locationName.name,
            isFavorite: false
          };
          setMapSelectedLocation(newLocation);
        }
        
        setError(null);
      } catch (error) {
        setError('Hava durumu verisi alınamadı.');
      }
    }, 600);
  };

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <AppHeader title="Hava Durumu" />
        <WeatherLoadingState />
      </SafeAreaView>
    );
  }

  if (error) {
    return (
      <SafeAreaView style={styles.container}>
        <AppHeader title="Hava Durumu" />
        <WeatherErrorState error={error} onRetry={handleRefresh} />
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <AppHeader
        title="Hava Durumu"
        rightButtons={[
          {
            icon: "settings",
            onPress: openLocationManager
          },
          {
            icon: "refresh-cw",
            onPress: handleRefresh
          }
        ]}
      />
      <View style={styles.content}>
        <ScrollView
          style={styles.scrollContent}
          showsVerticalScrollIndicator={false}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={handleRefresh}
              colors={[theme.colors.primary]}
              tintColor={theme.colors.primary}
            />
          }
        >
          <ScreenContainer>
            <WeatherMapComponent
              initialCoordinates={mapCoordinates}
              onLocationSelect={handleMapLocationSelect}
              style={styles.weatherMap}
            />
            <LocationTabs
              data={savedLocations}
              activeLocationId={currentLocation?.id || ''}
              onLocationPress={(location: any) => {
                const index = savedLocations.findIndex(loc => loc.id === location.id);
                if (index !== -1) setCurrentLocationIndex(index);
              }}
              onAddPress={openLocationManager}
              getLocationIcon={getLocationIcon}
            />
            {weatherData && (
              <View style={styles.section}>
                <CurrentWeatherCard
                  weatherData={weatherData}
                  location={mapSelectedLocation || currentLocation}
                  formattedTemperature={formattedTemperature}
                  formattedWindSpeed={formattedWindSpeed}
                />
              </View>
            )}
            <View style={styles.section}>
              <SectionTitle>Detaylı Bilgiler</SectionTitle>
              <WeatherDetailsGrid weatherDetails={weatherDetails} />
            </View>
            {weatherData?.fishing && (
              <View style={styles.section}>
                <SectionTitle>Balıkçılık Koşulları</SectionTitle>
                <FishingConditionsCard fishingData={weatherData.fishing} />
              </View>
            )}
            {weatherData?.hourly && (
              <View style={styles.section}>
                <SectionTitle>Saatlik Tahmin</SectionTitle>
                <HourlyForecast
                  data={weatherData.hourly}
                  getConditionIcon={getConditionIcon}
                  showDetails={true}
                />
              </View>
            )}
            {weatherData?.daily && (
              <View style={styles.section}>
                <SectionTitle>7 Günlük Tahmin</SectionTitle>
                <DailyForecastCard
                  dailyData={weatherData.daily}
                  formattedDailyTemps={formattedDailyTemps}
                />
              </View>
            )}
          </ScreenContainer>
        </ScrollView>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  content: {
    flex: 1,
  },
  scrollContent: {
    flex: 1,
  },
  section: {
    marginTop: theme.spacing.lg,
  },
  weatherMap: {
    height: 250,
    borderRadius: theme.borderRadius.xl,
    overflow: 'hidden',
    marginBottom: theme.spacing.md,
  },
});

export default WeatherScreen;