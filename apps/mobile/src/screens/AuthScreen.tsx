import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Alert,
  Dimensions,
  Image,
  TextInput,
  TouchableOpacity,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { theme } from '@fishivo/shared';
import { ScreenContainer, SuccessModal, SocialLoginButton, GoogleSignInButton, Icon } from '@fishivo/ui';
import { useAuth } from '@fishivo/hooks';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { RootStackParamList } from '@fishivo/shared';

const { width, height } = Dimensions.get('window');

const AuthScreen: React.FC = () => {
  const { signInWithGoogle, login, register, isLoading, user, authError, clearAuthError } = useAuth();
  const navigation = useNavigation<StackNavigationProp<RootStackParamList>>();
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [showErrorModal, setShowErrorModal] = useState(false);
  const [modalMessage, setModalMessage] = useState('');
  const [isLoginMode, setIsLoginMode] = useState(true);
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [fullName, setFullName] = useState('');
  const [username, setUsername] = useState('');
  const [showPassword, setShowPassword] = useState(false);

  useEffect(() => {
    if (authError) {
      setModalMessage(authError);
      setShowErrorModal(true);
    }
  }, [authError]);

  const handleGoogleLogin = async () => {
    try {
      await signInWithGoogle();
      // If we reach here, the sign-in was successful
      // The user state will be updated by AuthContext
      if (user) {
        setModalMessage(`Hoş geldin ${user.fullName || user.username}!`);
        setShowSuccessModal(true);
      }
    } catch (error) {
      // Error is already handled by AuthContext and set in authError
      console.error('Google login error:', error);
    }
  };

  const handleGoogleSignInSuccess = (data: any) => {
    console.log('✅ Google Sign-In success callback:', data);
    setModalMessage(`Google ile giriş başarılı!`);
    setShowSuccessModal(true);
  };

  const handleGoogleSignInError = (error: string) => {
    console.error('❌ Google Sign-In error callback:', error);
    setModalMessage(error);
    setShowErrorModal(true);
  };

  const handleSuccessModalClose = () => {
    setShowSuccessModal(false);
    navigation.reset({
      index: 0,
      routes: [{ name: 'MainTabs' }],
    });
  };

  const handleErrorModalClose = () => {
    setShowErrorModal(false);
    clearAuthError();
  };

  const handleEmailLogin = async () => {
    if (!email || !password) {
      setModalMessage('Email ve şifre gereklidir');
      setShowErrorModal(true);
      return;
    }

    try {
      await login(email, password);
      setModalMessage('Giriş başarılı!');
      setShowSuccessModal(true);
    } catch (error) {
      // Error is handled by AuthContext
    }
  };

  const handleEmailRegister = async () => {
    if (!email || !password || !fullName) {
      setModalMessage('Email, şifre ve ad soyad gereklidir');
      setShowErrorModal(true);
      return;
    }

    if (password.length < 6) {
      setModalMessage('Şifre en az 6 karakter olmalıdır');
      setShowErrorModal(true);
      return;
    }

    try {
      await register(email, password, fullName, username || undefined);
      setModalMessage('Kayıt başarılı! Email adresinizi kontrol edin.');
      setShowSuccessModal(true);
    } catch (error) {
      // Error is handled by AuthContext
    }
  };

  const toggleMode = () => {
    setIsLoginMode(!isLoginMode);
    setEmail('');
    setPassword('');
    setFullName('');
    setUsername('');
    clearAuthError();
  };

  return (
    <KeyboardAvoidingView 
      style={styles.container} 
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      <SafeAreaView style={styles.safeArea}>
        <ScrollView 
          contentContainerStyle={styles.scrollContainer}
          keyboardShouldPersistTaps="handled"
          showsVerticalScrollIndicator={false}
        >
          <ScreenContainer>
            {/* Logo ve Branding */}
            <View style={styles.logoSection}>
            <View style={styles.logoContainer}>
              <Image 
                source={require('../../android/app/src/main/res/drawable/splash_logo.png')}
                style={styles.logoImage}
                resizeMode="contain"
              />
            </View>
            <Text style={styles.appName}>Fishivo</Text>
            <Text style={styles.tagline}>Balıkçılık Deneyimini Paylaş</Text>
          </View>

          {/* Ana İçerik */}
          <View style={styles.contentSection}>
            <View style={styles.welcomeContainer}>
              <Text style={styles.welcomeTitle}>Hoş Geldin!</Text>
              <Text style={styles.welcomeSubtitle}>
                Fishivo topluluğuna katılmak için giriş yap
              </Text>
            </View>

            {/* Email/Password Form */}
            <View style={styles.formContainer}>
              <View style={styles.tabContainer}>
                <TouchableOpacity
                  style={[styles.tab, isLoginMode && styles.activeTab]}
                  onPress={() => setIsLoginMode(true)}
                >
                  <Text style={[styles.tabText, isLoginMode && styles.activeTabText]}>Giriş Yap</Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={[styles.tab, !isLoginMode && styles.activeTab]}
                  onPress={() => setIsLoginMode(false)}
                >
                  <Text style={[styles.tabText, !isLoginMode && styles.activeTabText]}>Kayıt Ol</Text>
                </TouchableOpacity>
              </View>

              <View style={styles.inputContainer}>
                {!isLoginMode && (
                  <>
                    <TextInput
                      style={styles.input}
                      placeholder="Ad Soyad"
                      value={fullName}
                      onChangeText={setFullName}
                      autoCapitalize="words"
                    />
                    <TextInput
                      style={styles.input}
                      placeholder="Kullanıcı Adı (Opsiyonel)"
                      value={username}
                      onChangeText={setUsername}
                      autoCapitalize="none"
                    />
                  </>
                )}
                <TextInput
                  style={styles.input}
                  placeholder="Email"
                  value={email}
                  onChangeText={setEmail}
                  keyboardType="email-address"
                  autoCapitalize="none"
                />
                <View style={styles.passwordContainer}>
                  <TextInput
                    style={styles.passwordInput}
                    placeholder="Şifre"
                    value={password}
                    onChangeText={setPassword}
                    secureTextEntry={!showPassword}
                  />
                  <TouchableOpacity
                    style={styles.passwordToggle}
                    onPress={() => setShowPassword(!showPassword)}
                  >
                    <Icon
                      name={showPassword ? 'eye-off' : 'eye'}
                      size={20}
                      color={theme.colors.textSecondary}
                    />
                  </TouchableOpacity>
                </View>
              </View>

              <TouchableOpacity
                style={styles.submitButton}
                onPress={isLoginMode ? handleEmailLogin : handleEmailRegister}
                disabled={isLoading}
              >
                <Text style={styles.submitButtonText}>
                  {isLoading ? 'Yükleniyor...' : (isLoginMode ? 'Giriş Yap' : 'Kayıt Ol')}
                </Text>
              </TouchableOpacity>

              <View style={styles.divider}>
                <View style={styles.dividerLine} />
                <Text style={styles.dividerText}>veya</Text>
                <View style={styles.dividerLine} />
              </View>
            </View>

            {/* Giriş Butonları */}
            <View style={styles.loginButtonsContainer}>
              {/* Yeni Google Sign-In Button */}
              <GoogleSignInButton
                onSuccess={handleGoogleSignInSuccess}
                onError={handleGoogleSignInError}
              />
              
              {/* Spacer */}
              <View style={{ height: theme.spacing.md }} />
              
              {/* Eski SocialLoginButton (Facebook için) */}
              <SocialLoginButton
                provider="google"
                onPress={handleGoogleLogin}
                loading={isLoading}
              />
            </View>

            {/* Özellikler */}
            <View style={styles.featuresContainer}>
              <View style={styles.featureRow}>
                <View style={styles.featureItem}>
                  <View style={styles.featureIconContainer}>
                    <Icon name="map-pin" size={20} color={theme.colors.primary} />
                  </View>
                  <Text style={styles.featureText}>Balık noktalarını keşfet</Text>
                </View>
                <View style={styles.featureItem}>
                  <View style={styles.featureIconContainer}>
                    <Icon name="activity" size={20} color={theme.colors.primary} />
                  </View>
                  <Text style={styles.featureText}>Avlarını kaydet</Text>
                </View>
              </View>
              <View style={styles.featureRow}>
                <View style={styles.featureItem}>
                  <View style={styles.featureIconContainer}>
                    <Icon name="users" size={20} color={theme.colors.primary} />
                  </View>
                  <Text style={styles.featureText}>Topluluğa katıl</Text>
                </View>
                <View style={styles.featureItem}>
                  <View style={styles.featureIconContainer}>
                    <Icon name="cloud-rain" size={20} color={theme.colors.primary} />
                  </View>
                  <Text style={styles.featureText}>Hava durumu</Text>
                </View>
              </View>
            </View>
          </View>

          {/* Footer */}
          <View style={styles.footer}>
            <Text style={styles.footerText}>
              Giriş yaparak{' '}
              <Text style={styles.linkText}>Kullanım Koşulları</Text>
              {' '}ve{' '}
              <Text style={styles.linkText}>Gizlilik Politikası</Text>
              'nı kabul etmiş olursunuz
            </Text>
            </View>
          </ScreenContainer>
        </ScrollView>

        <SuccessModal
          visible={showSuccessModal}
          title="Giriş Başarılı!"
          message={modalMessage}
          onClose={handleSuccessModalClose}
        />

        <SuccessModal
          visible={showErrorModal}
          title="Hata"
          message={modalMessage}
          onClose={handleErrorModalClose}
        />
      </SafeAreaView>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  safeArea: {
    flex: 1,
  },
  scrollContainer: {
    flexGrow: 1,
  },
  logoSection: {
    alignItems: 'center',
    paddingTop: theme.spacing.xxl,
    paddingBottom: theme.spacing.xl,
  },
  logoContainer: {
    width: 80,
    height: 80,
    borderRadius: theme.borderRadius.full,
    backgroundColor: theme.colors.surface,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: theme.spacing.lg,
    ...theme.shadows.lg,
  },
  logoImage: {
    width: '100%',
    height: '100%',
    borderRadius: theme.borderRadius.full,
  },
  appName: {
    fontSize: theme.typography['4xl'],
    color: theme.colors.text,
    fontWeight: theme.typography.bold,
    marginBottom: theme.spacing.xs,
  },
  tagline: {
    fontSize: theme.typography.base,
    color: theme.colors.textSecondary,
    textAlign: 'center',
  },
  contentSection: {
    flex: 1,
    justifyContent: 'center',
  },
  welcomeContainer: {
    alignItems: 'center',
    marginBottom: theme.spacing.xxl,
  },
  welcomeTitle: {
    fontSize: theme.typography['3xl'],
    color: theme.colors.text,
    fontWeight: theme.typography.bold,
    marginBottom: theme.spacing.sm,
  },
  welcomeSubtitle: {
    fontSize: theme.typography.base,
    color: theme.colors.textSecondary,
    textAlign: 'center',
    lineHeight: 22,
  },
  loginButtonsContainer: {
    gap: theme.spacing.md,
    marginBottom: theme.spacing.xxl,
  },
  featuresContainer: {
    gap: theme.spacing.lg,
  },
  featureRow: {
    flexDirection: 'row',
    gap: theme.spacing.lg,
  },
  featureItem: {
    flex: 1,
    alignItems: 'center',
  },
  featureIconContainer: {
    width: 40,
    height: 40,
    borderRadius: theme.borderRadius.md,
    backgroundColor: theme.colors.surface,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: theme.spacing.sm,
  },
  featureText: {
    fontSize: theme.typography.sm,
    color: theme.colors.textSecondary,
    textAlign: 'center',
    lineHeight: 18,
  },
  footer: {
    paddingTop: theme.spacing.xl,
    paddingBottom: theme.spacing.lg,
  },
  footerText: {
    fontSize: theme.typography.xs,
    color: theme.colors.textTertiary,
    textAlign: 'center',
    lineHeight: 16,
  },
  linkText: {
    color: theme.colors.primary,
    fontWeight: theme.typography.medium,
  },
  formContainer: {
    marginBottom: theme.spacing.xl,
  },
  tabContainer: {
    flexDirection: 'row',
    backgroundColor: theme.colors.surface,
    borderRadius: theme.borderRadius.lg,
    padding: 4,
    marginBottom: theme.spacing.lg,
  },
  tab: {
    flex: 1,
    paddingVertical: theme.spacing.sm,
    alignItems: 'center',
    borderRadius: theme.borderRadius.md,
  },
  activeTab: {
    backgroundColor: theme.colors.primary,
  },
  tabText: {
    fontSize: theme.typography.sm,
    color: theme.colors.textSecondary,
    fontWeight: theme.typography.medium,
  },
  activeTabText: {
    color: theme.colors.white,
  },
  inputContainer: {
    gap: theme.spacing.md,
    marginBottom: theme.spacing.lg,
  },
  input: {
    backgroundColor: theme.colors.surface,
    borderRadius: theme.borderRadius.lg,
    paddingHorizontal: theme.spacing.lg,
    paddingVertical: theme.spacing.md,
    fontSize: theme.typography.base,
    color: theme.colors.text,
    borderWidth: 1,
    borderColor: theme.colors.border,
  },
  passwordContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: theme.colors.surface,
    borderRadius: theme.borderRadius.lg,
    borderWidth: 1,
    borderColor: theme.colors.border,
  },
  passwordInput: {
    flex: 1,
    paddingHorizontal: theme.spacing.lg,
    paddingVertical: theme.spacing.md,
    fontSize: theme.typography.base,
    color: theme.colors.text,
  },
  passwordToggle: {
    paddingHorizontal: theme.spacing.md,
  },
  submitButton: {
    backgroundColor: theme.colors.primary,
    borderRadius: theme.borderRadius.lg,
    paddingVertical: theme.spacing.md,
    alignItems: 'center',
    marginBottom: theme.spacing.lg,
  },
  submitButtonText: {
    color: theme.colors.white,
    fontSize: theme.typography.base,
    fontWeight: theme.typography.semibold,
  },
  divider: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: theme.spacing.lg,
  },
  dividerLine: {
    flex: 1,
    height: 1,
    backgroundColor: theme.colors.border,
  },
  dividerText: {
    marginHorizontal: theme.spacing.md,
    fontSize: theme.typography.sm,
    color: theme.colors.textSecondary,
  },
});

export default AuthScreen;