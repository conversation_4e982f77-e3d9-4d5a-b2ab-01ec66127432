import React, { useEffect } from 'react';
import { StatusBar, LogBox } from 'react-native';
import { NavigationContainer } from '@react-navigation/native';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import { AppNavigator } from './src/navigation';
import { AuthProvider, FollowProvider } from '@fishivo/hooks';
import { LocationProvider } from '@fishivo/hooks';
import { theme } from '@fishivo/shared/theme';
import { GoogleSignInService } from '@fishivo/services';

// ViewTagResolver hatasını geçici olarak gizle (patch uygulanana kadar)
LogBox.ignoreLogs([
  'Mapbox [error] ViewTagResolver | view: null found with tag',
  'ViewTagResolver',
  '`new NativeEventEmitter()` was called with a non-null argument without the required `addListener` method',
  '`new NativeEventEmitter()` was called with a non-null argument without the required `removeListeners` method'
]);

// Ignore specific warnings
LogBox.ignoreLogs([
  'ViewPropTypes will be removed',
  'ColorPropType will be removed',
  'Sending `onAnimatedValueUpdate`',
  '`new NativeEventEmitter()` was called with a non-null argument without the required `removeListeners` method',
  'Require cycle:',
  'Possible Unhandled Promise Rejection'
]);

export default function App() {
  // Setup any app-wide configurations or listeners here
  useEffect(() => {
    // Environment info'yu logla
    console.log('🚀 App initialized successfully');
    
    // Google Sign-In'i configure et
    GoogleSignInService.configure();
  }, []);

  return (
    <GestureHandlerRootView style={{ flex: 1 }}>
    <SafeAreaProvider>
        <StatusBar 
          barStyle="light-content" 
          backgroundColor={theme.colors.background}
        />
      <AuthProvider>
        <FollowProvider>
          <LocationProvider>
            <NavigationContainer>
              <AppNavigator />
            </NavigationContainer>
          </LocationProvider>
        </FollowProvider>
      </AuthProvider>
    </SafeAreaProvider>
    </GestureHandlerRootView>
  );
}


