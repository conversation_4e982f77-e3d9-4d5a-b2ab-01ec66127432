@tailwind base;
@tailwind components;
@tailwind utilities;

@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');

@layer base {
  * {
    border-color: theme('colors.neutral.200');
  }
  
  html {
    font-family: 'Inter', system-ui, sans-serif;
    scroll-behavior: smooth;
  }
  
  body {
    background-color: theme('colors.neutral.50');
    color: theme('colors.neutral.900');
  }
  
  .dark body {
    background-color: #121212;
    color: theme('colors.neutral.100');
  }
}

@layer components {
  .glass-effect {
    @apply backdrop-blur-lg bg-white/10 border border-white/20;
  }
  
  .dark .glass-effect {
    @apply backdrop-blur-lg bg-black/10 border border-white/10;
  }
  
  .btn-primary {
    @apply bg-primary text-white px-6 py-3 rounded-lg font-semibold transition-all duration-300 hover:bg-primary/90 hover:transform hover:scale-105;
  }
  
  .btn-secondary {
    @apply bg-transparent border-2 border-primary text-primary px-6 py-3 rounded-lg font-semibold transition-all duration-300 hover:bg-primary hover:text-white;
  }
  
  .card {
    @apply bg-white/80 backdrop-blur-lg rounded-xl p-6 shadow-lg border border-white/20 transition-all duration-300 hover:shadow-xl hover:transform hover:scale-105;
  }
  
  .dark .card {
    @apply bg-neutral-800/80 border-neutral-700/20;
  }
  
  .neon-glow {
    @apply shadow-lg;
    box-shadow: 0 0 5px theme('colors.primary'), 0 0 10px theme('colors.primary'), 0 0 15px theme('colors.primary');
  }
  
  .dark .neon-glow {
    box-shadow: 0 0 5px theme('colors.water'), 0 0 10px theme('colors.water'), 0 0 15px theme('colors.water');
  }
  
  .gradient-text {
    @apply bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent;
  }
  
  .hero-gradient {
    @apply bg-gradient-to-br from-blue-50 via-white to-green-50;
  }
  
  .dark .hero-gradient {
    @apply bg-gradient-to-br from-neutral-900 via-neutral-800 to-neutral-900;
  }
}

 