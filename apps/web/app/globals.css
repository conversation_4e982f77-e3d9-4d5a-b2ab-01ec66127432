@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');

:root {
  --primary: #007AFF;
  --secondary: #5856D6;
  --success: #34C759;
  --warning: #FF9500;
  --error: #FF3B30;
  --water: #4A90E2;
  --fish: #FF6B35;
  --nature: #7ED321;
  --dark-primary: #121212;
  --dark-secondary: #1E1E1E;
  --dark-card: #2A2A2A;
}

* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

html {
  scroll-behavior: smooth;
}

html,
body {
  max-width: 100vw;
  overflow-x: hidden;
  font-family: 'Inter', system-ui, sans-serif;
  background-color: white;
  color: #111827;
  transition: background-color 0.3s ease, color 0.3s ease;
}

.dark body {
  background-color: var(--dark-primary);
  color: white;
}

.gradient-text {
  background: linear-gradient(135deg, var(--primary), var(--secondary));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.hero-gradient {
  background: linear-gradient(135deg, #dbeafe, white, #dcfce7);
}

.dark .hero-gradient {
  background: linear-gradient(135deg, var(--dark-primary), var(--dark-secondary), var(--dark-primary));
}

.card-hover {
  transition: all 0.3s ease-in-out;
}

.card-hover:hover {
  transform: translateY(-8px);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.dark .card-hover:hover {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.4), 0 4px 6px -2px rgba(0, 0, 0, 0.3);
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

@keyframes float {
  0% { transform: translateY(0px); }
  50% { transform: translateY(-20px); }
  100% { transform: translateY(0px); }
}

.animate-wave {
  animation: wave 2s ease-in-out infinite;
}

@keyframes wave {
  0%, 100% { transform: rotate(0deg); }
  25% { transform: rotate(5deg); }
  75% { transform: rotate(-5deg); }
}

.glass-effect {
  backdrop-filter: blur(4px);
  background-color: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.dark .glass-effect {
  background-color: rgba(17, 24, 39, 0.1);
  border: 1px solid rgba(107, 114, 128, 0.2);
}

.neon-glow {
  box-shadow: 0 0 20px rgba(0, 122, 255, 0.3);
}

.dark .neon-glow {
  box-shadow: 0 0 20px rgba(0, 122, 255, 0.5);
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: .5; }
}

.animate-bounce {
  animation: bounce 1s infinite;
}

@keyframes bounce {
  0%, 100% {
    transform: translateY(-25%);
    animation-timing-function: cubic-bezier(0.8, 0, 1, 1);
  }
  50% {
    transform: translateY(0);
    animation-timing-function: cubic-bezier(0, 0, 0.2, 1);
  }
} 