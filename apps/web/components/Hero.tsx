'use client'

import Link from 'next/link'

export default function Hero() {
  return (
    <section className="relative min-h-screen flex items-center justify-center overflow-hidden 
                        bg-gradient-to-br from-blue-50 via-white to-green-50 
                        dark:from-dark-primary dark:via-dark-secondary dark:to-dark-primary pt-16">
      
      {/* Background Elements */}
      <div className="absolute inset-0 overflow-hidden">
        {/* Animated Background Shapes */}
        <div className="absolute top-20 left-10 w-32 h-32 bg-primary/10 rounded-full animate-float"></div>
        <div className="absolute top-40 right-20 w-24 h-24 bg-water/20 rounded-full animate-float" style={{animationDelay: '2s'}}></div>
        <div className="absolute bottom-32 left-1/4 w-40 h-40 bg-nature/10 rounded-full animate-float" style={{animationDelay: '4s'}}></div>
        <div className="absolute bottom-20 right-1/3 w-28 h-28 bg-fish/20 rounded-full animate-float" style={{animationDelay: '3s'}}></div>
        
        {/* Grid <PERSON> */}
        <div className="absolute inset-0 bg-[linear-gradient(rgba(0,122,255,0.03)_1px,transparent_1px),linear-gradient(90deg,rgba(0,122,255,0.03)_1px,transparent_1px)] bg-[size:50px_50px] dark:bg-[linear-gradient(rgba(255,255,255,0.02)_1px,transparent_1px),linear-gradient(90deg,rgba(255,255,255,0.02)_1px,transparent_1px)]"></div>
      </div>

      {/* Content */}
      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        
        {/* Badge */}
        <div className="inline-flex items-center px-4 py-2 bg-white/10 dark:bg-gray-900/20 
                        backdrop-blur-sm border border-white/20 dark:border-gray-700/20 
                        rounded-full text-sm font-medium text-gray-700 dark:text-gray-300 mb-8
                        hover:bg-white/20 dark:hover:bg-gray-900/30 transition-all duration-300">
          <span className="w-2 h-2 bg-green-500 rounded-full mr-2 animate-pulse"></span>
          Türkiye'nin #1 Balıkçılık Sosyal Ağı
        </div>

        {/* Main Heading */}
        <h1 className="text-4xl sm:text-5xl lg:text-7xl font-bold mb-6 leading-tight">
          <span className="text-gray-900 dark:text-white">Avlarını Paylaş,</span>
          <br />
          <span className="gradient-text">Topluluğa Katıl</span>
        </h1>

        {/* Subtitle */}
        <p className="text-lg sm:text-xl lg:text-2xl text-gray-600 dark:text-gray-300 
                      max-w-3xl mx-auto mb-12 leading-relaxed">
          Fishivo ile balık avı deneyimlerini paylaş, yeni teknikler öğren ve 
          <span className="text-primary font-semibold"> Türkiye'nin en büyük balıkçı topluluğuna</span> katıl.
        </p>

        {/* CTA Buttons */}
        <div className="flex flex-col sm:flex-row items-center justify-center gap-4 mb-16">
          <Link
            href="/dashboard"
            className="group relative bg-gradient-to-r from-primary to-secondary text-white 
                      px-8 py-4 rounded-2xl font-semibold text-lg shadow-2xl 
                      hover:shadow-primary/25 dark:hover:shadow-primary/40
                      transform hover:scale-105 transition-all duration-300 
                      flex items-center space-x-2 neon-glow min-w-[200px]"
          >
            <span>Hemen Başla</span>
            <svg className="w-5 h-5 group-hover:translate-x-1 transition-transform" 
                 fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} 
                    d="M13 7l5 5m0 0l-5 5m5-5H6" />
            </svg>
          </Link>

          <Link
            href="#features"
            className="group px-8 py-4 bg-white/10 dark:bg-gray-900/20 backdrop-blur-sm 
                      border border-white/20 dark:border-gray-700/20 text-gray-900 dark:text-white 
                      rounded-2xl font-semibold text-lg hover:bg-white/20 dark:hover:bg-gray-900/30
                      transform hover:scale-105 transition-all duration-300 
                      flex items-center space-x-2 min-w-[200px]"
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} 
                    d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1.5a1.5 1.5 0 011.5 1.5v1a1.5 1.5 0 01-1.5 1.5H9m12 0a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <span>Özellikleri Keşfet</span>
          </Link>
        </div>

        {/* Stats */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-8 max-w-4xl mx-auto">
          {[
            { number: '50K+', label: 'Aktif Balıkçı', icon: '👥' },
            { number: '100K+', label: 'Paylaşılan Av', icon: '🎣' },
            { number: '1000+', label: 'Avlama Noktası', icon: '📍' },
            { number: '5/5', label: 'Kullanıcı Puanı', icon: '⭐' },
          ].map((stat, index) => (
            <div
              key={index}
              className="group text-center p-6 bg-white/10 dark:bg-gray-900/20 backdrop-blur-sm 
                        border border-white/20 dark:border-gray-700/20 rounded-2xl
                        hover:bg-white/20 dark:hover:bg-gray-900/30 transition-all duration-300
                        hover:scale-105 hover:shadow-lg"
            >
              <div className="text-3xl mb-2 group-hover:scale-110 transition-transform duration-300">
                {stat.icon}
              </div>
              <div className="text-2xl font-bold text-gray-900 dark:text-white mb-1">
                {stat.number}
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-400">
                {stat.label}
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Scroll Indicator */}
      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2">
        <div className="flex flex-col items-center space-y-2 text-gray-600 dark:text-gray-400">
          <span className="text-sm font-medium">Aşağı Kaydır</span>
          <svg className="w-5 h-5 animate-bounce" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 14l-7 7m0 0l-7-7m7 7V3" />
          </svg>
        </div>
      </div>
    </section>
  )
} 