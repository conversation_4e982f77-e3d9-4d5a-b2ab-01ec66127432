'use client'

import { theme } from '@fishivo/shared/theme'

export default function Features() {
  const features = [
    {
      icon: '🎣',
      title: 'Avlarını Paylaş',
      description: 'Ya<PERSON>adığın balıkları fotoğrafla, detayları ekle ve topluluğa göster. GPS konum bilgisi ile avlama noktanı da kaydet.',
      gradient: 'from-blue-500 to-cyan-500',
      bgColor: 'bg-blue-50 dark:bg-blue-900/20'
    },
    {
      icon: '🌍',
      title: 'Balıkçı Topluluğu',
      description: 'Deneyimli balıkçıları takip et, yorum yap, beğen. Sorularını sor ve uzmanlardan tavsiyeleri al.',
      gradient: 'from-green-500 to-emerald-500',
      bgColor: 'bg-green-50 dark:bg-green-900/20'
    },
    {
      icon: '📍',
      title: 'Avlama Noktaları',
      description: 'En iyi balık avlama noktalarını keşfet. <PERSON><PERSON><PERSON> balıkçıların tavsiyelerini gör ve yeni yerler dene.',
      gradient: 'from-red-500 to-pink-500',
      bgColor: 'bg-red-50 dark:bg-red-900/20'
    },
    {
      icon: '🌤️',
      title: 'Hava Durumu',
      description: 'Anlık hava durumu ve balık avı için en uygun zamanları öğren. Rüzgar, dalga ve sıcaklık bilgileri.',
      gradient: 'from-yellow-500 to-orange-500',
      bgColor: 'bg-yellow-50 dark:bg-yellow-900/20'
    },
    {
      icon: '🎯',
      title: 'Ekipman Takibi',
      description: 'Kullandığın oltaları, yemleri ve diğer ekipmanları kaydet. Hangi ekipmanla ne yakaladığını analiz et.',
      gradient: 'from-purple-500 to-indigo-500',
      bgColor: 'bg-purple-50 dark:bg-purple-900/20'
    },
    {
      icon: '📊',
      title: 'İstatistikler',
      description: 'Avlama geçmişini takip et, en başarılı dönemleri gör. Kişisel rekorlarını ve gelişimini izle.',
      gradient: 'from-teal-500 to-cyan-500',
      bgColor: 'bg-teal-50 dark:bg-teal-900/20'
    }
  ]

  return (
    <section id="features" className="py-20 bg-white dark:bg-dark-primary">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        
        {/* Section Header */}
        <div className="text-center mb-16">
          <div className="inline-flex items-center px-4 py-2 bg-primary/10 dark:bg-primary/20 
                          rounded-full text-primary font-medium text-sm mb-4">
            <span className="w-2 h-2 bg-primary rounded-full mr-2"></span>
            Özellikler
          </div>
          <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold text-gray-900 dark:text-white mb-6">
            Balıkçılık Deneyimini
            <span className="gradient-text block">Bir Üst Seviyeye Çıkar</span>
          </h2>
          <p className="text-lg text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
            Fishivo ile balık avı tutkunlugunu dijital dünyada yaşa. Profesyonel araçlar ve topluluk desteği ile avlama çık.
          </p>
        </div>

        {/* Features Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {features.map((feature, index) => (
            <div
              key={index}
              className="group relative p-8 bg-white dark:bg-dark-card rounded-2xl 
                        shadow-card dark:shadow-dark-card card-hover
                        border border-gray-100 dark:border-gray-700/50
                        hover:border-primary/20 dark:hover:border-primary/30
                        transition-all duration-300"
            >
              {/* Icon Container */}
              <div className={`w-16 h-16 ${feature.bgColor} rounded-2xl flex items-center justify-center mb-6
                              group-hover:scale-110 transition-transform duration-300`}>
                <span className="text-3xl">{feature.icon}</span>
              </div>

              {/* Content */}
              <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-4 group-hover:text-primary 
                            transition-colors duration-300">
                {feature.title}
              </h3>
              <p className="text-gray-600 dark:text-gray-300 leading-relaxed">
                {feature.description}
              </p>

              {/* Hover Gradient Background */}
              <div className={`absolute inset-0 bg-gradient-to-br ${feature.gradient} opacity-0 
                              group-hover:opacity-5 dark:group-hover:opacity-10 rounded-2xl 
                              transition-opacity duration-300`}></div>

              {/* Corner Accent */}
              <div className="absolute top-4 right-4 w-3 h-3 bg-primary/20 rounded-full 
                             group-hover:bg-primary/40 transition-colors duration-300"></div>
            </div>
          ))}
        </div>

        {/* Bottom CTA */}
        <div className="text-center mt-16">
          <div className="inline-flex flex-col sm:flex-row items-center gap-4">
            <div className="text-gray-600 dark:text-gray-300">
              <span className="font-semibold text-gray-900 dark:text-white">50,000+</span> balıkçı bu özellikleri kullanıyor
            </div>
            <div className="flex -space-x-2">
              {[1, 2, 3, 4, 5].map((i) => (
                <div
                  key={i}
                  className="w-8 h-8 bg-gradient-to-br from-primary to-secondary rounded-full 
                            border-2 border-white dark:border-dark-primary flex items-center justify-center"
                >
                  <span className="text-white text-xs font-bold">👤</span>
                </div>
              ))}
              <div className="w-8 h-8 bg-gray-200 dark:bg-gray-700 rounded-full border-2 
                             border-white dark:border-dark-primary flex items-center justify-center">
                <span className="text-gray-600 dark:text-gray-300 text-xs font-bold">+</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
} 